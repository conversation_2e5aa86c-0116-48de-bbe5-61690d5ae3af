{"name": "isms-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-sql": "^2.3.0", "@tauri-apps/plugin-store": "^2.3.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "postcss": "^8.5.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.2", "zustand": "^5.0.5"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/node": "^24.0.4", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "vite": "^6.0.3"}}