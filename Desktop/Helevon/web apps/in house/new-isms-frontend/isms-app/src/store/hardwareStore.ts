import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { BluetoothDeviceInfo } from '../services/hardwareService';

interface HardwareState {
  // Label printer settings
  selectedLabelPrinter: BluetoothDeviceInfo | null;
  availableBluetoothDevices: BluetoothDeviceInfo[];
  isDiscoveringDevices: boolean;
  
  // Scanner settings
  scannerEnabled: boolean;
  lastScannedBarcode: string | null;
  
  // Actions
  setSelectedLabelPrinter: (printer: BluetoothDeviceInfo | null) => void;
  setAvailableBluetoothDevices: (devices: BluetoothDeviceInfo[]) => void;
  setIsDiscoveringDevices: (discovering: boolean) => void;
  setScannerEnabled: (enabled: boolean) => void;
  setLastScannedBarcode: (barcode: string | null) => void;
  
  // Helper functions
  getSelectedPrinterAddress: () => string | null;
}

export const useHardwareStore = create<HardwareState>()(
  persist(
    (set, get) => ({
      // Initial state
      selectedLabelPrinter: null,
      availableBluetoothDevices: [],
      isDiscoveringDevices: false,
      scannerEnabled: true,
      lastScannedBarcode: null,

      // Actions
      setSelectedLabelPrinter: (printer) => set({ selectedLabelPrinter: printer }),
      
      setAvailableBluetoothDevices: (devices) => set({ availableBluetoothDevices: devices }),
      
      setIsDiscoveringDevices: (discovering) => set({ isDiscoveringDevices: discovering }),
      
      setScannerEnabled: (enabled) => set({ scannerEnabled: enabled }),
      
      setLastScannedBarcode: (barcode) => set({ lastScannedBarcode: barcode }),

      // Helper functions
      getSelectedPrinterAddress: () => {
        const state = get();
        return state.selectedLabelPrinter?.address || null;
      },
    }),
    {
      name: 'hardware-settings',
      partialize: (state) => ({
        selectedLabelPrinter: state.selectedLabelPrinter,
        scannerEnabled: state.scannerEnabled,
      }),
    }
  )
);
