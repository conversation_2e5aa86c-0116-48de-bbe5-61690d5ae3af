import React, { useState, useEffect } from 'react';
import { useAppStore } from '../store/appStore';
import { useAuthStore } from '../store/authStore';
import { useHardwareStore } from '../store/hardwareStore';
import { hardwareService } from '../services/hardwareService';
import { useToast } from '../hooks/useToast';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

const Settings: React.FC = () => {
  const { theme, setTheme } = useAppStore();
  const { user } = useAuthStore();
  const { showSuccess, showError } = useToast();

  // Hardware store
  const {
    selectedLabelPrinter,
    availableBluetoothDevices,
    isDiscoveringDevices,
    scannerEnabled,
    setSelectedLabelPrinter,
    setAvailableBluetoothDevices,
    setIsDiscoveringDevices,
    setScannerEnabled,
  } = useHardwareStore();

  // Local state for testing
  const [isTestingPrinter, setIsTestingPrinter] = useState(false);

  const themes = [
    { value: 'light', label: 'Light', description: 'Light theme' },
    { value: 'dark', label: 'Dark', description: 'Dark theme' },
    { value: 'system', label: 'System', description: 'Follow system preference' },
  ];

  // Bluetooth device discovery
  const discoverBluetoothDevices = async () => {
    try {
      setIsDiscoveringDevices(true);
      const devices = await hardwareService.listBluetoothDevices();
      setAvailableBluetoothDevices(devices);
      showSuccess(`Found ${devices.length} Bluetooth devices`);
    } catch (error) {
      console.error('Failed to discover Bluetooth devices:', error);
      showError('Failed to discover Bluetooth devices');
    } finally {
      setIsDiscoveringDevices(false);
    }
  };

  // Test printer functionality
  const testPrinter = async () => {
    if (!selectedLabelPrinter) {
      showError('Please select a label printer first');
      return;
    }

    try {
      setIsTestingPrinter(true);
      await hardwareService.printTestLabel(selectedLabelPrinter.address);
      showSuccess('Test label sent to printer successfully');
    } catch (error) {
      console.error('Failed to test printer:', error);
      showError('Failed to test printer');
    } finally {
      setIsTestingPrinter(false);
    }
  };

  // Load devices on component mount
  useEffect(() => {
    discoverBluetoothDevices();
  }, []);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Settings</h1>
        <p className="text-muted-foreground mt-1">
          Manage your application preferences and account settings.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Theme Settings */}
        <Card title="Appearance" description="Customize the look and feel of the application">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">Theme</label>
              <div className="mt-2 space-y-2">
                {themes.map((themeOption) => (
                  <label key={themeOption.value} className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="theme"
                      value={themeOption.value}
                      checked={theme === themeOption.value}
                      onChange={() => setTheme(themeOption.value as any)}
                      className="h-4 w-4 text-primary focus:ring-primary border-border"
                    />
                    <div>
                      <div className="text-sm font-medium text-foreground">
                        {themeOption.label}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {themeOption.description}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </Card>

        {/* Profile Settings */}
        <Card title="Profile" description="Your account information">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">Full Name</label>
              <div className="mt-1 text-sm text-muted-foreground">
                {user?.full_name}
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-foreground">Email</label>
              <div className="mt-1 text-sm text-muted-foreground">
                {user?.email}
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-foreground">Role</label>
              <div className="mt-1 text-sm text-muted-foreground capitalize">
                {user?.role}
              </div>
            </div>

            <div className="pt-4">
              <Button variant="outline" size="sm">
                Change Password
              </Button>
            </div>
          </div>
        </Card>

        {/* System Information */}
        <Card title="System Information" description="Application and system details">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">Application Version</label>
              <div className="mt-1 text-sm text-muted-foreground">
                v1.0.0
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-foreground">Database Status</label>
              <div className="mt-1 flex items-center space-x-2">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-muted-foreground">Connected</span>
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-foreground">Last Sync</label>
              <div className="mt-1 text-sm text-muted-foreground">
                Never (Offline Mode)
              </div>
            </div>
          </div>
        </Card>

        {/* Permissions */}
        <Card title="Permissions" description="Your current access permissions">
          <div className="space-y-2">
            {user?.permissions?.map((permission) => (
              <div key={permission} className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-foreground capitalize">
                  {permission.replace('_', ' ')}
                </span>
              </div>
            ))}
          </div>
        </Card>

        {/* Label Printer Settings */}
        <Card title="Label Printer" description="Configure Bluetooth label printer for barcode printing">
          <div className="space-y-4">
            {/* Bluetooth Device Discovery */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-foreground">Available Bluetooth Devices</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={discoverBluetoothDevices}
                  disabled={isDiscoveringDevices}
                >
                  {isDiscoveringDevices ? 'Discovering...' : 'Refresh Devices'}
                </Button>
              </div>

              <div className="space-y-2 max-h-40 overflow-y-auto">
                {availableBluetoothDevices.length === 0 ? (
                  <div className="text-sm text-muted-foreground p-2 border border-dashed rounded">
                    No Bluetooth devices found. Click "Refresh Devices" to scan.
                  </div>
                ) : (
                  availableBluetoothDevices.map((device) => (
                    <div
                      key={device.address}
                      className={`p-2 border rounded cursor-pointer transition-colors ${
                        selectedLabelPrinter?.address === device.address
                          ? 'border-primary bg-primary/10'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => setSelectedLabelPrinter(device)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium text-foreground">{device.name}</div>
                          <div className="text-xs text-muted-foreground">{device.address}</div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {device.connected && (
                            <div className="h-2 w-2 bg-green-500 rounded-full" title="Connected" />
                          )}
                          {device.paired && (
                            <div className="h-2 w-2 bg-blue-500 rounded-full" title="Paired" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Selected Printer Info */}
            {selectedLabelPrinter && (
              <div className="p-3 bg-muted rounded">
                <div className="text-sm font-medium text-foreground mb-1">Selected Printer</div>
                <div className="text-sm text-muted-foreground">
                  {selectedLabelPrinter.name} ({selectedLabelPrinter.address})
                </div>
              </div>
            )}

            {/* Test Print Button */}
            <div className="pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={testPrinter}
                disabled={!selectedLabelPrinter || isTestingPrinter}
              >
                {isTestingPrinter ? 'Testing...' : 'Test Print'}
              </Button>
            </div>
          </div>
        </Card>

        {/* Scanner Settings */}
        <Card title="Barcode Scanner" description="Configure barcode scanner settings">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="scanner-enabled"
                checked={scannerEnabled}
                onChange={(e) => setScannerEnabled(e.target.checked)}
                className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
              />
              <label htmlFor="scanner-enabled" className="text-sm font-medium text-foreground">
                Enable Barcode Scanner
              </label>
            </div>
            <div className="text-xs text-muted-foreground">
              When enabled, the scanner will automatically detect barcode input from the Inateck BCST-21-AI scanner.
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Settings;
