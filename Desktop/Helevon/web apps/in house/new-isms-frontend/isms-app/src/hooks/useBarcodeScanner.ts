import { useEffect, useCallback, useRef } from 'react';
import { useHardwareStore } from '../store/hardwareStore';

interface BarcodeScannerOptions {
  onScan?: (barcode: string) => void;
  enabled?: boolean;
  minLength?: number;
  maxLength?: number;
  timeout?: number; // Time in ms to wait for complete barcode input
}

export const useBarcodeScanner = (options: BarcodeScannerOptions = {}) => {
  const {
    onScan,
    enabled = true,
    minLength = 8,
    maxLength = 20,
    timeout = 100,
  } = options;

  const { scannerEnabled, setLastScannedBarcode } = useHardwareStore();
  const barcodeBuffer = useRef<string>('');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastInputTime = useRef<number>(0);

  const processBarcodeInput = useCallback((barcode: string) => {
    if (barcode.length >= minLength && barcode.length <= maxLength) {
      setLastScannedBarcode(barcode);
      onScan?.(barcode);
    }
  }, [onScan, minLength, maxLength, setLastScannedBarcode]);

  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    // Only process if scanner is enabled globally and locally
    if (!scannerEnabled || !enabled) return;

    // Ignore if user is typing in an input field (unless it's a barcode input field)
    const target = event.target as HTMLElement;
    const isInputField = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;
    const isBarcodeInput = target.classList.contains('barcode-input') || target.dataset.barcodeInput === 'true';
    
    // If it's an input field but not specifically a barcode input, ignore
    if (isInputField && !isBarcodeInput) return;

    const currentTime = Date.now();
    const timeDiff = currentTime - lastInputTime.current;

    // If too much time has passed since last input, reset buffer
    // This helps distinguish between manual typing and scanner input
    if (timeDiff > 500) {
      barcodeBuffer.current = '';
    }

    lastInputTime.current = currentTime;

    // Handle Enter key (scanner typically sends this at the end)
    if (event.key === 'Enter') {
      event.preventDefault();
      
      if (barcodeBuffer.current.length > 0) {
        processBarcodeInput(barcodeBuffer.current);
        barcodeBuffer.current = '';
      }
      return;
    }

    // Handle regular characters
    if (event.key.length === 1) {
      // Prevent default only if we're actively building a barcode
      if (barcodeBuffer.current.length > 0 || /[0-9]/.test(event.key)) {
        event.preventDefault();
      }

      barcodeBuffer.current += event.key;

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set a timeout to process the barcode if no more input comes
      timeoutRef.current = setTimeout(() => {
        if (barcodeBuffer.current.length > 0) {
          processBarcodeInput(barcodeBuffer.current);
          barcodeBuffer.current = '';
        }
      }, timeout);
    }
  }, [scannerEnabled, enabled, processBarcodeInput, timeout]);

  useEffect(() => {
    if (enabled && scannerEnabled) {
      document.addEventListener('keydown', handleKeyPress, true);
      
      return () => {
        document.removeEventListener('keydown', handleKeyPress, true);
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }
  }, [enabled, scannerEnabled, handleKeyPress]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    isEnabled: enabled && scannerEnabled,
    lastScannedBarcode: useHardwareStore.getState().lastScannedBarcode,
  };
};
