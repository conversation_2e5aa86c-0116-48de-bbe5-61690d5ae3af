import { publicInvoke, secureInvoke } from '../utils/apiInterceptor';
import { LoginResponse, User } from '../types';

export const authService = {
  // Login (public - no token required)
  login: async (email: string, password: string): Promise<LoginResponse> => {
    return await publicInvoke('login', { email, password });
  },

  // Logout (requires token)
  logout: async (): Promise<string> => {
    return await secureInvoke('logout');
  },

  // Get current user (requires token)
  getCurrentUser: async (): Promise<User> => {
    return await secureInvoke('get_current_user');
  },

  // Validate session (requires token)
  validateSession: async (): Promise<boolean> => {
    try {
      await secureInvoke('validate_user_session');
      return true;
    } catch (error) {
      console.error('Session validation failed:', error);
      return false;
    }
  },

  // Check user permission (requires token)
  checkPermission: async (permission: string): Promise<boolean> => {
    try {
      const hasPermission = await secureInvoke<boolean>('check_user_permission', { permission });
      return hasPermission;
    } catch (error) {
      console.error('Permission check failed:', error);
      return false;
    }
  },
};
