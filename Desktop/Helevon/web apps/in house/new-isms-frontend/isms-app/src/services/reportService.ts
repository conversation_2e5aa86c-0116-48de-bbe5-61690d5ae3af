import { secureInvoke } from '../utils/apiInterceptor';

export interface SalesReport {
  date: string;
  total_sales: number;
  total_orders: number;
  average_order_value: number;
}

export interface ProductSalesReport {
  product_id: number;
  product_name: string;
  quantity_sold: number;
  total_revenue: number;
}

export interface InventoryReport {
  total_products: number;
  total_value: number;
  low_stock_count: number;
  expiring_soon_count: number;
  categories: {
    category: string;
    product_count: number;
    total_value: number;
  }[];
}

export interface DashboardStats {
  today_sales: number;
  today_orders: number;
  total_products: number;
  low_stock_count: number;
  total_inventory_value: number;
  recent_orders: any[];
}

export const reportService = {
  // Get sales report for date range
  getSalesReport: async (startDate: string, endDate: string): Promise<SalesReport[]> => {
    return await secureInvoke('get_sales_report', { 
      start_date: startDate, 
      end_date: endDate 
    });
  },

  // Get product sales report
  getProductSalesReport: async (startDate: string, endDate: string): Promise<ProductSalesReport[]> => {
    return await secureInvoke('get_product_sales_report', { 
      start_date: startDate, 
      end_date: endDate 
    });
  },

  // Get inventory report
  getInventoryReport: async (): Promise<InventoryReport> => {
    return await secureInvoke('get_inventory_report');
  },

  // Get dashboard statistics
  getDashboardStats: async (): Promise<DashboardStats> => {
    return await secureInvoke('get_dashboard_stats');
  },

  // Export sales report to CSV
  exportSalesReport: async (startDate: string, endDate: string): Promise<string> => {
    return await secureInvoke('export_sales_report', { 
      start_date: startDate, 
      end_date: endDate 
    });
  },

  // Export inventory report to CSV
  exportInventoryReport: async (): Promise<string> => {
    return await secureInvoke('export_inventory_report');
  },
};
