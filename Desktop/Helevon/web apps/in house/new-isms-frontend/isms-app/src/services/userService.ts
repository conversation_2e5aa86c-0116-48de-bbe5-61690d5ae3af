import { secureInvoke } from '../utils/apiInterceptor';
import { User, Role, Permission } from '../types';

export const userService = {
  // Get all users
  getUsers: async (): Promise<User[]> => {
    return await secureInvoke('get_users');
  },

  // Create a new user
  createUser: async (userData: {
    email: string;
    fullName: string;
    role: string;
    password: string;
  }): Promise<number> => {
    return await secureInvoke('create_user', {
      email: userData.email,
      fullName: userData.fullName,
      role: userData.role,
      password: userData.password,
    });
  },

  // Update an existing user
  updateUser: async (
    userId: number,
    userData: {
      email: string;
      fullName: string;
      role: string;
    }
  ): Promise<void> => {
    return await secureInvoke('update_user', {
      userId,
      email: userData.email,
      fullName: userData.fullName,
      role: userData.role,
    });
  },

  // Delete a user
  deleteUser: async (userId: number): Promise<void> => {
    return await secureInvoke('delete_user', { userId });
  },

  // Get all roles
  getRoles: async (): Promise<Role[]> => {
    return await secureInvoke('get_roles');
  },

  // Get all permissions
  getPermissions: async (): Promise<Permission[]> => {
    return await secureInvoke('get_permissions');
  },
};
