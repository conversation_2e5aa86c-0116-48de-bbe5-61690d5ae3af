import { secureInvoke } from '../utils/apiInterceptor';

// Bluetooth device information interface
export interface BluetoothDeviceInfo {
  name: string;
  address: string;
  services: string[];
  connected: boolean;
  paired: boolean;
}

export const hardwareService = {
  // Barcode generation
  generateBarcodeData: async (productSku: string, barcodeType?: string): Promise<string> => {
    return await secureInvoke('generate_barcode_data', { 
      productSku, 
      barcodeType: barcodeType || 'EAN13' 
    });
  },

  // Bluetooth device discovery
  listBluetoothDevices: async (): Promise<BluetoothDeviceInfo[]> => {
    return await secureInvoke('list_bluetooth_devices');
  },

  // Label printing
  printBarcodeLabel: async (
    productSku: string,
    productName: string,
    productPrice: number,
    printerAddress?: string
  ): Promise<string> => {
    return await secureInvoke('print_barcode_label', {
      productSku,
      productName,
      productPrice,
      printerAddress,
    });
  },

  // Test label printing
  printTestLabel: async (printerAddress: string): Promise<string> => {
    return await secureInvoke('print_test_label', { printerAddress });
  },

  // Receipt printing
  printReceiptHtml: async (receiptHtml: string): Promise<string> => {
    return await secureInvoke('print_receipt_html', { receiptHtml });
  },
};
