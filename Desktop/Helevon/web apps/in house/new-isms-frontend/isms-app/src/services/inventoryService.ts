import { secureInvoke } from '../utils/apiInterceptor';
import { Product, Supplier, InventoryMovement, CreateProductRequest, UpdateStockRequest } from '../types';

export const inventoryService = {
  // Product management
  getProducts: async (): Promise<Product[]> => {
    return await secureInvoke('get_products');
  },

  createProduct: async (productData: CreateProductRequest): Promise<number> => {
    return await secureInvoke('create_product', { productData });
  },

  updateProduct: async (productId: number, productData: CreateProductRequest): Promise<void> => {
    return await secureInvoke('update_product', { productId, productData });
  },

  deleteProduct: async (productId: number): Promise<void> => {
    return await secureInvoke('delete_product', { productId });
  },

  updateStock: async (productId: number, stockData: UpdateStockRequest): Promise<void> => {
    return await secureInvoke('update_stock', { productId, stockData });
  },

  getLowStockProducts: async (): Promise<Product[]> => {
    return await secureInvoke('get_low_stock_products');
  },

  // Supplier management
  getSuppliers: async (): Promise<Supplier[]> => {
    return await secureInvoke('get_suppliers');
  },

  createSupplier: async (supplierData: {
    name: string;
    contactName?: string;
    email?: string;
    phone?: string;
    address?: string;
  }): Promise<number> => {
    return await secureInvoke('create_supplier', {
      name: supplierData.name,
      contactName: supplierData.contactName,
      email: supplierData.email,
      phone: supplierData.phone,
      address: supplierData.address,
    });
  },

  updateSupplier: async (
    supplierId: number,
    supplierData: {
      name: string;
      contactName?: string;
      email?: string;
      phone?: string;
      address?: string;
    }
  ): Promise<void> => {
    return await secureInvoke('update_supplier', {
      supplierId,
      name: supplierData.name,
      contactName: supplierData.contactName,
      email: supplierData.email,
      phone: supplierData.phone,
      address: supplierData.address,
    });
  },

  deleteSupplier: async (supplierId: number): Promise<void> => {
    return await secureInvoke('delete_supplier', { supplierId });
  },

  // Inventory movements
  getInventoryMovements: async (productId?: number): Promise<InventoryMovement[]> => {
    return await secureInvoke('get_inventory_movements', { productId });
  },
};
