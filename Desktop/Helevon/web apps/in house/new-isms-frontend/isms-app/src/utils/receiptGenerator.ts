export interface ReceiptOrder {
  id: number;
  customer_name?: string;
  total_amount: number;
  created_at: string;
  cashier_name?: string;
}

export interface ReceiptItem {
  product_name: string;
  quantity: number;
  price: number;
}

export interface ReceiptData {
  order: ReceiptOrder;
  items: ReceiptItem[];
  companyInfo: {
    name: string;
    address: string;
    phone: string;
    email?: string;
  };
  paymentMethod: string;
  amountPaid: number;
  change: number;
}

export const generateReceiptHTML = (data: ReceiptData): string => {
  const { order, items, companyInfo, paymentMethod, amountPaid, change } = data;
  
  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;
  const formatDate = (date: string) => new Date(date).toLocaleString();

  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Receipt #${order.id}</title>
  <style>
    @media print {
      @page {
        size: 80mm auto;
        margin: 0;
      }
      body {
        margin: 0;
        padding: 5mm;
      }
    }
    
    body {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.2;
      max-width: 80mm;
      margin: 0 auto;
      padding: 10px;
      background: white;
      color: black;
    }
    
    .header {
      text-align: center;
      border-bottom: 1px dashed #000;
      padding-bottom: 10px;
      margin-bottom: 10px;
    }
    
    .company-name {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .company-info {
      font-size: 10px;
      line-height: 1.3;
    }
    
    .receipt-info {
      margin-bottom: 10px;
      font-size: 11px;
    }
    
    .items {
      border-bottom: 1px dashed #000;
      padding-bottom: 10px;
      margin-bottom: 10px;
    }
    
    .item {
      margin-bottom: 5px;
    }
    
    .item-name {
      font-weight: bold;
    }
    
    .item-details {
      display: flex;
      justify-content: space-between;
      font-size: 11px;
    }
    
    .totals {
      margin-bottom: 10px;
    }
    
    .total-line {
      display: flex;
      justify-content: space-between;
      margin-bottom: 2px;
    }
    
    .total-line.grand-total {
      font-weight: bold;
      font-size: 14px;
      border-top: 1px solid #000;
      padding-top: 5px;
      margin-top: 5px;
    }
    
    .payment-info {
      border-top: 1px dashed #000;
      padding-top: 10px;
      margin-top: 10px;
    }
    
    .footer {
      text-align: center;
      margin-top: 15px;
      font-size: 10px;
      border-top: 1px dashed #000;
      padding-top: 10px;
    }
    
    .flex-between {
      display: flex;
      justify-content: space-between;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="company-name">${companyInfo.name}</div>
    <div class="company-info">
      ${companyInfo.address}<br>
      ${companyInfo.phone}
      ${companyInfo.email ? `<br>${companyInfo.email}` : ''}
    </div>
  </div>

  <div class="receipt-info">
    <div class="flex-between">
      <span>Receipt #:</span>
      <span>${order.id}</span>
    </div>
    <div class="flex-between">
      <span>Date:</span>
      <span>${formatDate(order.created_at)}</span>
    </div>
    <div class="flex-between">
      <span>Cashier:</span>
      <span>${order.cashier_name || 'N/A'}</span>
    </div>
    ${order.customer_name ? `
    <div class="flex-between">
      <span>Customer:</span>
      <span>${order.customer_name}</span>
    </div>
    ` : ''}
  </div>

  <div class="items">
    ${items.map(item => `
    <div class="item">
      <div class="item-name">${item.product_name}</div>
      <div class="item-details">
        <span>${item.quantity} x ${formatCurrency(item.price)}</span>
        <span>${formatCurrency(item.quantity * item.price)}</span>
      </div>
    </div>
    `).join('')}
  </div>

  <div class="totals">
    <div class="total-line">
      <span>Subtotal:</span>
      <span>${formatCurrency(order.total_amount)}</span>
    </div>
    <div class="total-line">
      <span>Tax:</span>
      <span>${formatCurrency(0)}</span>
    </div>
    <div class="total-line grand-total">
      <span>TOTAL:</span>
      <span>${formatCurrency(order.total_amount)}</span>
    </div>
  </div>

  <div class="payment-info">
    <div class="flex-between">
      <span>Payment Method:</span>
      <span>${paymentMethod.toUpperCase()}</span>
    </div>
    <div class="flex-between">
      <span>Amount Paid:</span>
      <span>${formatCurrency(amountPaid)}</span>
    </div>
    ${change > 0 ? `
    <div class="flex-between">
      <span>Change:</span>
      <span>${formatCurrency(change)}</span>
    </div>
    ` : ''}
  </div>

  <div class="footer">
    <div>Thank you for your business!</div>
    <div>Please keep this receipt for your records</div>
  </div>
</body>
</html>
  `.trim();
};

export const printReceipt = async (receiptData: ReceiptData): Promise<void> => {
  const receiptHTML = generateReceiptHTML(receiptData);
  
  // Create a new window for printing
  const printWindow = window.open('', '_blank', 'width=300,height=600');
  
  if (!printWindow) {
    throw new Error('Failed to open print window. Please allow popups for this site.');
  }

  printWindow.document.write(receiptHTML);
  printWindow.document.close();
  
  // Wait for content to load, then print
  printWindow.onload = () => {
    printWindow.print();
    
    // Close the window after printing (with a small delay)
    setTimeout(() => {
      printWindow.close();
    }, 1000);
  };
};
