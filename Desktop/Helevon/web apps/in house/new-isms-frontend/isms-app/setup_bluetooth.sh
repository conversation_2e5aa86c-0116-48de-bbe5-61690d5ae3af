#!/bin/bash

# Bluetooth Printer Setup Script for ISMS
# This script sets up the Katasymbol T50M Pro printer for use with the ISMS application

echo "🔧 ISMS Bluetooth Printer Setup"
echo "================================"

PRINTER_ADDRESS="A4:93:40:76:50:E9"
PRINTER_NAME="T0147B2502128591"

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ Please do not run this script as root. Run as your normal user."
   exit 1
fi

echo "📋 Setting up Bluetooth printer: $PRINTER_NAME ($PRINTER_ADDRESS)"

# Step 1: Add user to dialout group
echo "🔐 Adding user to dialout group for RFCOMM access..."
sudo usermod -a -G dialout $USER
if [ $? -eq 0 ]; then
    echo "✅ User added to dialout group successfully"
else
    echo "❌ Failed to add user to dialout group"
fi

# Step 2: Install required packages
echo "📦 Installing required Bluetooth packages..."
sudo apt update
sudo apt install -y bluetooth bluez bluez-tools rfcomm socat

# Step 3: Start Bluetooth service
echo "🔄 Starting Bluetooth service..."
sudo systemctl enable bluetooth
sudo systemctl start bluetooth

# Step 4: Pair and trust the printer
echo "🔗 Pairing with printer..."
bluetoothctl << EOF
power on
agent on
default-agent
scan on
EOF

sleep 3

bluetoothctl << EOF
pair $PRINTER_ADDRESS
trust $PRINTER_ADDRESS
connect $PRINTER_ADDRESS
exit
EOF

# Step 5: Create RFCOMM binding
echo "🔌 Creating RFCOMM binding..."
sudo rfcomm bind /dev/rfcomm0 $PRINTER_ADDRESS 1

# Step 6: Set permissions on RFCOMM device
echo "🔒 Setting permissions on RFCOMM device..."
sudo chmod 666 /dev/rfcomm0
sudo chown $USER:dialout /dev/rfcomm0

# Step 7: Test the connection
echo "🧪 Testing printer connection..."
if [ -e /dev/rfcomm0 ]; then
    echo "✅ RFCOMM device created successfully"
    
    # Test with a simple command
    echo "📄 Sending test print..."
    echo -e "SIZE 40 mm, 31 mm\nGAP 2 mm, 0 mm\nCLS\nTEXT 50,50,\"3\",0,1,1,\"SETUP TEST\"\nTEXT 50,80,\"2\",0,1,1,\"$(date)\"\nPRINT 1,1" > /dev/rfcomm0
    
    if [ $? -eq 0 ]; then
        echo "✅ Test print sent successfully!"
    else
        echo "❌ Failed to send test print"
    fi
else
    echo "❌ RFCOMM device not created"
fi

# Step 8: Create udev rule for persistent permissions
echo "📝 Creating udev rule for persistent permissions..."
sudo tee /etc/udev/rules.d/99-rfcomm.rules > /dev/null << EOF
KERNEL=="rfcomm[0-9]*", GROUP="dialout", MODE="0666"
EOF

sudo udevadm control --reload-rules

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "📋 Summary:"
echo "  • User added to dialout group"
echo "  • Bluetooth packages installed"
echo "  • Printer paired and trusted"
echo "  • RFCOMM device created at /dev/rfcomm0"
echo "  • Permissions configured"
echo ""
echo "⚠️  IMPORTANT: You need to log out and log back in for group changes to take effect!"
echo ""
echo "🧪 Manual Test Commands:"
echo "  • Check device: bluetoothctl info $PRINTER_ADDRESS"
echo "  • Test print: echo -e 'SIZE 40 mm, 31 mm\\nCLS\\nTEXT 50,50,\"3\",0,1,1,\"TEST\"\\nPRINT 1,1' > /dev/rfcomm0"
echo "  • Check RFCOMM: ls -la /dev/rfcomm*"
echo ""
echo "🔧 Troubleshooting:"
echo "  • If permission denied: sudo chmod 666 /dev/rfcomm0"
echo "  • If device not found: sudo rfcomm bind /dev/rfcomm0 $PRINTER_ADDRESS 1"
echo "  • If connection fails: bluetoothctl connect $PRINTER_ADDRESS"
echo ""
echo "✨ You can now test the printer in the ISMS Settings page!"
