# Bluetooth Printer Troubleshooting Guide

## Issue: Katasymbol T50M Pro Printer Not Printing

### Symptoms
- Bluetooth connects but disconnects quickly
- Printer shows as available but doesn't print
- TSPL commands are generated but not executed

### Root Cause Analysis

The main issue is that the current implementation doesn't establish a proper RFCOMM (Serial Port Profile) connection to send the TSPL commands to the printer. The printer connects via Bluetooth but there's no data channel established.

### Solutions Implemented

#### 1. Enhanced Bluetooth Connection (Primary)
**Location:** `src-tauri/src/hardware.rs` - `print_label_bluetooth_linux()`

**Improvements:**
- Added proper device pairing check
- Connection stabilization delay
- Better error handling

#### 2. System Command Fallbacks (Alternative)
**Location:** `src-tauri/src/hardware.rs` - `print_label_system_command()`

**Methods:**
- RFCOMM device creation (`/dev/rfcomm0`)
- Network connection via netcat (port 9100)
- CUPS printing system integration

### Manual Setup Instructions

#### Method 1: RFCOMM Setup (Recommended)

1. **Pair the printer manually:**
   ```bash
   bluetoothctl
   scan on
   pair A4:93:40:76:50:E9
   trust A4:93:40:76:50:E9
   connect A4:93:40:76:50:E9
   exit
   ```

2. **Create RFCOMM binding:**
   ```bash
   sudo rfcomm bind /dev/rfcomm0 A4:93:40:76:50:E9 1
   ```

3. **Test TSPL commands manually:**
   ```bash
   echo -e "SIZE 40 mm, 31 mm\nGAP 2 mm, 0 mm\nDIRECTION 1\nREFERENCE 0,0\nOFFSET 0 mm\nSET PEEL OFF\nSET CUTTER OFF\nSET PARTIAL_CUTTER OFF\nSET TEAR ON\nCLS\n\nBARCODE 50,50,\"128\",60,1,0,2,2,\"123456789012\"\nTEXT 50,120,\"3\",0,1,1,\"Test Product\"\nTEXT 50,150,\"2\",0,1,1,\"$9.99\"\nTEXT 50,180,\"1\",0,1,1,\"123456789012\"\n\nPRINT 1,1" > /dev/rfcomm0
   ```

#### Method 2: Network Printing (Alternative)

Some thermal printers support network printing on port 9100:

```bash
echo -e "SIZE 40 mm, 31 mm\nGAP 2 mm, 0 mm\nDIRECTION 1\nREFERENCE 0,0\nOFFSET 0 mm\nSET PEEL OFF\nSET CUTTER OFF\nSET PARTIAL_CUTTER OFF\nSET TEAR ON\nCLS\n\nBARCODE 50,50,\"128\",60,1,0,2,2,\"123456789012\"\nTEXT 50,120,\"3\",0,1,1,\"Test Product\"\nTEXT 50,150,\"2\",0,1,1,\"$9.99\"\nTEXT 50,180,\"1\",0,1,1,\"123456789012\"\n\nPRINT 1,1" | nc A4:93:40:76:50:E9 9100
```

#### Method 3: CUPS Integration

1. **Add printer to CUPS:**
   ```bash
   sudo lpadmin -p katasymbol_printer -E -v bluetooth://A4:93:40:76:50:E9 -m raw
   ```

2. **Print via lp command:**
   ```bash
   echo "TSPL commands here" | lp -d katasymbol_printer
   ```

### Application Usage

#### In Settings Page:
1. **Refresh Devices** - Scan for available Bluetooth devices
2. **Select Printer** - Choose "T0147B2502128591" from the list
3. **Test Print (Bluetooth)** - Try the primary Bluetooth method
4. **Test Print (System)** - Try alternative system command methods

#### Debugging Steps:

1. **Check Bluetooth Status:**
   ```bash
   bluetoothctl show
   bluetoothctl devices
   ```

2. **Verify Printer Connection:**
   ```bash
   bluetoothctl info A4:93:40:76:50:E9
   ```

3. **Check RFCOMM Devices:**
   ```bash
   rfcomm -a
   ls -la /dev/rfcomm*
   ```

4. **Monitor System Logs:**
   ```bash
   journalctl -f | grep -i bluetooth
   dmesg | grep -i bluetooth
   ```

### Common Issues and Solutions

#### Issue: "Device not found"
**Solution:** Ensure printer is in pairing mode and within range

#### Issue: "Permission denied on /dev/rfcomm0"
**Solution:** Add user to dialout group:
```bash
sudo usermod -a -G dialout $USER
```

#### Issue: "Connection timeout"
**Solution:** 
- Restart Bluetooth service: `sudo systemctl restart bluetooth`
- Reset printer by turning off/on
- Clear Bluetooth cache: `sudo rm -rf /var/lib/bluetooth/*/cache`

#### Issue: "Printer connects but disconnects immediately"
**Solution:**
- This is normal behavior - printer disconnects when not actively receiving data
- The application should connect, send data, then disconnect

### Receipt Printing Fix

The receipt printing error has been resolved with:

1. **Improved Error Handling:** Better promise handling in `printReceipt()`
2. **Popup Detection:** Clear error message if popups are blocked
3. **Fallback Timing:** Multiple load event handlers for reliability

### Testing the Fixes

1. **Build the application:**
   ```bash
   npm run build
   cargo build
   ```

2. **Test receipt printing:**
   - Complete a sale in POS
   - Ensure popups are allowed
   - Check for successful receipt generation

3. **Test barcode printing:**
   - Go to Settings → Label Printer
   - Select the Katasymbol printer
   - Try both test print methods
   - Check console logs for TSPL command output

### Next Steps for Production

1. **Implement proper RFCOMM socket connection** in Rust using system calls
2. **Add printer driver detection** to automatically choose the best method
3. **Create printer setup wizard** for guided configuration
4. **Add printer status monitoring** to detect connection issues

### Support Information

- **Printer Model:** Katasymbol T50M Pro
- **Bluetooth Address:** A4:93:40:76:50:E9
- **Protocol:** TSPL (Thermal Printer Standard Language)
- **Connection:** Bluetooth SPP (Serial Port Profile)
- **Label Size:** 40mm x 31mm with 2mm gap
