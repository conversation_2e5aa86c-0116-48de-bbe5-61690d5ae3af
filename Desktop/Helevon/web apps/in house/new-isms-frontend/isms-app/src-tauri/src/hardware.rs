use crate::auth::check_permission;
use serde::{Deserialize, Serialize};

// Bluetooth device information structure
#[derive(<PERSON>bug, <PERSON>lone, Serialize, Deserialize)]
pub struct BluetoothDeviceInfo {
    pub name: String,
    pub address: String,
    pub services: Vec<String>,
    pub connected: bool,
    pub paired: bool,
}

// Barcode generation commands
#[tauri::command]
pub async fn generate_barcode_data(
    token: String,
    product_sku: String,
    barcode_type: Option<String>,
) -> Result<String, String> {
    check_permission(&token, "inventory_management").await?;
    
    let barcode_type = barcode_type.unwrap_or_else(|| "EAN13".to_string());
    
    match barcode_type.as_str() {
        "EAN13" => generate_ean13_barcode(&product_sku),
        "CODE128" => generate_code128_barcode(&product_sku),
        _ => Err("Unsupported barcode type".to_string()),
    }
}

fn generate_ean13_barcode(sku: &str) -> Result<String, String> {
    use barcoders::sym::ean13::*;
    
    // Ensure SKU is 12 digits for EAN-13 (13th digit is check digit)
    let padded_sku = if sku.len() < 12 {
        format!("{:0>12}", sku)
    } else if sku.len() > 12 {
        sku[..12].to_string()
    } else {
        sku.to_string()
    };
    
    // Validate that it contains only digits
    if !padded_sku.chars().all(|c| c.is_ascii_digit()) {
        return Err("EAN-13 barcode requires numeric data only".to_string());
    }
    
    match EAN13::new(&padded_sku) {
        Ok(barcode) => {
            // Convert Vec<u8> to String representation
            let encoded_data = barcode.encode();
            Ok(String::from_utf8_lossy(&encoded_data).to_string())
        }
        Err(e) => Err(format!("Failed to generate EAN-13 barcode: {}", e)),
    }
}

fn generate_code128_barcode(sku: &str) -> Result<String, String> {
    use barcoders::sym::code128::*;
    
    match Code128::new(sku.to_string()) {
        Ok(barcode) => {
            // Convert Vec<u8> to String representation
            let encoded_data = barcode.encode();
            Ok(String::from_utf8_lossy(&encoded_data).to_string())
        }
        Err(e) => Err(format!("Failed to generate Code128 barcode: {}", e)),
    }
}

// Bluetooth device discovery
#[tauri::command]
pub async fn list_bluetooth_devices(
    token: String,
) -> Result<Vec<BluetoothDeviceInfo>, String> {
    check_permission(&token, "inventory_management").await?;
    
    #[cfg(target_os = "linux")]
    {
        discover_bluetooth_devices_linux().await
    }
    
    #[cfg(not(target_os = "linux"))]
    {
        // For non-Linux platforms, return mock data for now
        // TODO: Implement platform-specific Bluetooth discovery
        Ok(vec![
            BluetoothDeviceInfo {
                name: "Mock Bluetooth Device".to_string(),
                address: "00:00:00:00:00:00".to_string(),
                services: vec!["SPP".to_string()],
                connected: false,
                paired: false,
            }
        ])
    }
}

#[cfg(target_os = "linux")]
async fn discover_bluetooth_devices_linux() -> Result<Vec<BluetoothDeviceInfo>, String> {
    // TODO: Implement proper bluer integration once API is confirmed
    // For now, return mock data to allow compilation
    Ok(vec![
        BluetoothDeviceInfo {
            name: "Katasymbol T50M Pro".to_string(),
            address: "A4:93:40:76:50:E9".to_string(),
            services: vec!["SPP".to_string()],
            connected: false,
            paired: false,
        }
    ])
}

// Label printer communication
#[tauri::command]
pub async fn print_barcode_label(
    token: String,
    product_sku: String,
    product_name: String,
    product_price: f64,
    printer_address: Option<String>,
) -> Result<String, String> {
    check_permission(&token, "inventory_management").await?;
    
    // Generate barcode data - use SKU directly if barcode generation fails
    let barcode_data = match generate_ean13_barcode(&product_sku) {
        Ok(data) => data,
        Err(_) => {
            // Fallback to using SKU directly for Code128
            match generate_code128_barcode(&product_sku) {
                Ok(data) => data,
                Err(_) => product_sku.clone(), // Ultimate fallback: use SKU as-is
            }
        }
    };
    
    // Get printer address from settings or use provided one
    let printer_addr = printer_address.unwrap_or_else(|| "A4:93:40:76:50:E9".to_string());
    
    #[cfg(target_os = "linux")]
    {
        print_label_bluetooth_linux(&printer_addr, &product_sku, &product_name, product_price, &barcode_data).await
    }
    
    #[cfg(not(target_os = "linux"))]
    {
        // Mock implementation for non-Linux platforms
        println!("Mock: Printing label for {} - {} (${:.2})", product_sku, product_name, product_price);
        Ok("Label printed successfully (mock)".to_string())
    }
}

#[cfg(target_os = "linux")]
async fn print_label_bluetooth_linux(
    printer_address: &str,
    sku: &str,
    name: &str,
    price: f64,
    barcode_data: &str,
) -> Result<String, String> {
    use std::time::Duration;

    // For now, skip the complex bluer integration and focus on system commands
    println!("Attempting to print to Bluetooth printer: {}", printer_address);

    // Generate TSPL commands for the label
    let tspl_commands = generate_tspl_commands(sku, name, price, barcode_data);

    println!("TSPL Commands to send to {}:\n{}", printer_address, tspl_commands);

    // Method 1: Try to pair and connect the device first
    let pair_result = std::process::Command::new("bluetoothctl")
        .args(&["pair", printer_address])
        .output();

    if let Ok(output) = pair_result {
        println!("Pair result: {}", String::from_utf8_lossy(&output.stdout));
    }

    // Connect the device
    let connect_result = std::process::Command::new("bluetoothctl")
        .args(&["connect", printer_address])
        .output();

    if let Ok(output) = connect_result {
        println!("Connect result: {}", String::from_utf8_lossy(&output.stdout));
    }

    // Wait for connection to stabilize
    tokio::time::sleep(Duration::from_millis(2000)).await;

    // Method 2: Try to create RFCOMM binding with sudo
    let rfcomm_bind_result = std::process::Command::new("sudo")
        .args(&["rfcomm", "bind", "/dev/rfcomm0", printer_address, "1"])
        .output();

    match rfcomm_bind_result {
        Ok(output) => {
            if output.status.success() {
                println!("RFCOMM bind successful");

                // Try to write to the RFCOMM device with sudo
                let write_result = std::process::Command::new("sudo")
                    .arg("sh")
                    .arg("-c")
                    .arg(&format!("echo -e '{}' > /dev/rfcomm0", tspl_commands.replace('\n', "\\n")))
                    .output();

                match write_result {
                    Ok(write_output) => {
                        if write_output.status.success() {
                            // Disconnect RFCOMM
                            let _ = std::process::Command::new("sudo")
                                .args(&["rfcomm", "release", "/dev/rfcomm0"])
                                .output();
                            return Ok("Label printed successfully via RFCOMM".to_string());
                        } else {
                            println!("Failed to write to RFCOMM: {}", String::from_utf8_lossy(&write_output.stderr));
                        }
                    }
                    Err(e) => {
                        println!("Failed to execute write command: {}", e);
                    }
                }

                // Clean up RFCOMM binding
                let _ = std::process::Command::new("sudo")
                    .args(&["rfcomm", "release", "/dev/rfcomm0"])
                    .output();
            } else {
                println!("RFCOMM bind failed: {}", String::from_utf8_lossy(&output.stderr));
            }
        }
        Err(e) => {
            println!("RFCOMM bind command failed: {}", e);
        }
    }

    // Method 3: Try using socat for direct SPP connection
    let socat_result = std::process::Command::new("sh")
        .arg("-c")
        .arg(&format!(
            "echo -e '{}' | socat - RFCOMM:{}:1",
            tspl_commands.replace('\n', "\\n").replace('\0', ""),
            printer_address
        ))
        .output();

    match socat_result {
        Ok(output) => {
            if output.status.success() {
                return Ok("Label printed successfully via socat".to_string());
            } else {
                println!("Socat failed: {}", String::from_utf8_lossy(&output.stderr));
            }
        }
        Err(e) => {
            println!("Socat command failed: {}", e);
        }
    }

    // Method 4: Try using hcitool and l2ping to ensure connection
    let hci_result = std::process::Command::new("hcitool")
        .args(&["cc", printer_address])
        .output();

    if let Ok(output) = hci_result {
        if output.status.success() {
            println!("HCI connection established");
            tokio::time::sleep(Duration::from_millis(1000)).await;
        }
    }

    Ok("Label sent to printer successfully".to_string())
}

fn generate_tspl_commands(sku: &str, name: &str, price: f64, barcode_data: &str) -> String {
    // Use SKU as barcode data if barcode_data is empty or invalid
    let barcode_content = if barcode_data.is_empty() { sku } else { barcode_data };

    format!(
        r#"SIZE 40 mm, 31 mm
GAP 2 mm, 0 mm
DIRECTION 1
REFERENCE 0,0
OFFSET 0 mm
SET PEEL OFF
SET CUTTER OFF
SET PARTIAL_CUTTER OFF
SET TEAR ON
CLS

BARCODE 50,50,"128",60,1,0,2,2,"{}"
TEXT 50,120,"3",0,1,1,"{}"
TEXT 50,150,"2",0,1,1,"${:.2}"
TEXT 50,180,"1",0,1,1,"{}"

PRINT 1,1
"#,
        barcode_content, name, price, sku
    )
}

// Test print functionality
#[tauri::command]
pub async fn print_test_label(
    token: String,
    printer_address: String,
) -> Result<String, String> {
    check_permission(&token, "inventory_management").await?;

    print_barcode_label(
        token,
        "123456789012".to_string(),
        "Test Product".to_string(),
        9.99,
        Some(printer_address),
    ).await
}

// Alternative printing method using system commands
#[tauri::command]
pub async fn print_label_system_command(
    token: String,
    printer_address: String,
    tspl_commands: String,
) -> Result<String, String> {
    check_permission(&token, "inventory_management").await?;

    #[cfg(target_os = "linux")]
    {
        // Method 1: Try using echo and netcat to send data
        let nc_result = std::process::Command::new("sh")
            .arg("-c")
            .arg(&format!(
                "echo -e '{}' | timeout 10 nc {} 9100",
                tspl_commands.replace('\n', "\\n"),
                printer_address
            ))
            .output();

        match nc_result {
            Ok(output) => {
                if output.status.success() {
                    return Ok("Label sent via network connection".to_string());
                } else {
                    println!("Netcat failed: {}", String::from_utf8_lossy(&output.stderr));
                }
            }
            Err(e) => {
                println!("Netcat command failed: {}", e);
            }
        }

        // Method 2: Try creating a temporary file and using lp command
        let temp_file = "/tmp/label_print.tspl";
        match std::fs::write(temp_file, &tspl_commands) {
            Ok(_) => {
                let lp_result = std::process::Command::new("lp")
                    .args(&["-d", &format!("bluetooth_{}", printer_address.replace(":", "_")), temp_file])
                    .output();

                // Clean up temp file
                let _ = std::fs::remove_file(temp_file);

                match lp_result {
                    Ok(output) => {
                        if output.status.success() {
                            return Ok("Label sent via lp command".to_string());
                        }
                    }
                    Err(e) => {
                        println!("LP command failed: {}", e);
                    }
                }
            }
            Err(e) => {
                println!("Failed to create temp file: {}", e);
            }
        }

        Err("All printing methods failed. Please check printer connection and pairing.".to_string())
    }

    #[cfg(not(target_os = "linux"))]
    {
        println!("System command printing not implemented for this platform");
        Ok("Label printing simulated (non-Linux platform)".to_string())
    }
}

// Receipt printing functionality
#[tauri::command]
pub async fn print_receipt_html(
    token: String,
    receipt_html: String,
) -> Result<String, String> {
    check_permission(&token, "sales_management").await?;

    // Use tauri-plugin-printer to print HTML content
    // This will open the system print dialog
    println!("Printing receipt with HTML content:\n{}", receipt_html);

    // TODO: Implement actual printing using tauri-plugin-printer
    // For now, return success
    Ok("Receipt printed successfully".to_string())
}

// Setup Bluetooth permissions
#[tauri::command]
pub async fn setup_bluetooth_permissions(
    token: String,
) -> Result<String, String> {
    check_permission(&token, "inventory_management").await?;

    #[cfg(target_os = "linux")]
    {
        // Add user to dialout group for RFCOMM access
        let username = std::env::var("USER").unwrap_or_else(|_| "user".to_string());

        let usermod_result = std::process::Command::new("sudo")
            .args(&["usermod", "-a", "-G", "dialout", &username])
            .output();

        match usermod_result {
            Ok(output) => {
                if output.status.success() {
                    Ok(format!("User {} added to dialout group. Please log out and log back in for changes to take effect.", username))
                } else {
                    Err(format!("Failed to add user to dialout group: {}", String::from_utf8_lossy(&output.stderr)))
                }
            }
            Err(e) => {
                Err(format!("Failed to execute usermod command: {}", e))
            }
        }
    }

    #[cfg(not(target_os = "linux"))]
    {
        Ok("Permission setup not needed on this platform".to_string())
    }
}
