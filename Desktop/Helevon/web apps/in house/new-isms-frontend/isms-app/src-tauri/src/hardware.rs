use crate::auth::check_permission;
use serde::{Deserialize, Serialize};

// Bluetooth device information structure
#[derive(<PERSON>bug, <PERSON>lone, Serialize, Deserialize)]
pub struct BluetoothDeviceInfo {
    pub name: String,
    pub address: String,
    pub services: Vec<String>,
    pub connected: bool,
    pub paired: bool,
}

// Barcode generation commands
#[tauri::command]
pub async fn generate_barcode_data(
    token: String,
    product_sku: String,
    barcode_type: Option<String>,
) -> Result<String, String> {
    check_permission(&token, "inventory_management").await?;
    
    let barcode_type = barcode_type.unwrap_or_else(|| "EAN13".to_string());
    
    match barcode_type.as_str() {
        "EAN13" => generate_ean13_barcode(&product_sku),
        "CODE128" => generate_code128_barcode(&product_sku),
        _ => Err("Unsupported barcode type".to_string()),
    }
}

fn generate_ean13_barcode(sku: &str) -> Result<String, String> {
    use barcoders::sym::ean13::*;
    
    // Ensure SKU is 12 digits for EAN-13 (13th digit is check digit)
    let padded_sku = if sku.len() < 12 {
        format!("{:0>12}", sku)
    } else if sku.len() > 12 {
        sku[..12].to_string()
    } else {
        sku.to_string()
    };
    
    // Validate that it contains only digits
    if !padded_sku.chars().all(|c| c.is_ascii_digit()) {
        return Err("EAN-13 barcode requires numeric data only".to_string());
    }
    
    match EAN13::new(&padded_sku) {
        Ok(barcode) => {
            // Convert Vec<u8> to String representation
            let encoded_data = barcode.encode();
            Ok(String::from_utf8_lossy(&encoded_data).to_string())
        }
        Err(e) => Err(format!("Failed to generate EAN-13 barcode: {}", e)),
    }
}

fn generate_code128_barcode(sku: &str) -> Result<String, String> {
    use barcoders::sym::code128::*;
    
    match Code128::new(sku.to_string()) {
        Ok(barcode) => {
            // Convert Vec<u8> to String representation
            let encoded_data = barcode.encode();
            Ok(String::from_utf8_lossy(&encoded_data).to_string())
        }
        Err(e) => Err(format!("Failed to generate Code128 barcode: {}", e)),
    }
}

// Bluetooth device discovery
#[tauri::command]
pub async fn list_bluetooth_devices(
    token: String,
) -> Result<Vec<BluetoothDeviceInfo>, String> {
    check_permission(&token, "inventory_management").await?;
    
    #[cfg(target_os = "linux")]
    {
        discover_bluetooth_devices_linux().await
    }
    
    #[cfg(not(target_os = "linux"))]
    {
        // For non-Linux platforms, return mock data for now
        // TODO: Implement platform-specific Bluetooth discovery
        Ok(vec![
            BluetoothDeviceInfo {
                name: "Mock Bluetooth Device".to_string(),
                address: "00:00:00:00:00:00".to_string(),
                services: vec!["SPP".to_string()],
                connected: false,
                paired: false,
            }
        ])
    }
}

#[cfg(target_os = "linux")]
async fn discover_bluetooth_devices_linux() -> Result<Vec<BluetoothDeviceInfo>, String> {
    // TODO: Implement proper bluer integration once API is confirmed
    // For now, return mock data to allow compilation
    Ok(vec![
        BluetoothDeviceInfo {
            name: "Katasymbol T50M Pro".to_string(),
            address: "A4:93:40:76:50:E9".to_string(),
            services: vec!["SPP".to_string()],
            connected: false,
            paired: false,
        }
    ])
}

// Label printer communication
#[tauri::command]
pub async fn print_barcode_label(
    token: String,
    product_sku: String,
    product_name: String,
    product_price: f64,
    printer_address: Option<String>,
) -> Result<String, String> {
    check_permission(&token, "inventory_management").await?;
    
    // Generate barcode data
    let barcode_data = generate_ean13_barcode(&product_sku)?;
    
    // Get printer address from settings or use provided one
    let printer_addr = printer_address.unwrap_or_else(|| "A4:93:40:76:50:E9".to_string());
    
    #[cfg(target_os = "linux")]
    {
        print_label_bluetooth_linux(&printer_addr, &product_sku, &product_name, product_price, &barcode_data).await
    }
    
    #[cfg(not(target_os = "linux"))]
    {
        // Mock implementation for non-Linux platforms
        println!("Mock: Printing label for {} - {} (${:.2})", product_sku, product_name, product_price);
        Ok("Label printed successfully (mock)".to_string())
    }
}

#[cfg(target_os = "linux")]
async fn print_label_bluetooth_linux(
    printer_address: &str,
    sku: &str,
    name: &str,
    price: f64,
    barcode_data: &str,
) -> Result<String, String> {
    // TODO: Implement proper bluer integration once API is confirmed
    // Generate TSPL commands for the label
    let tspl_commands = generate_tspl_commands(sku, name, price, barcode_data);

    // For now, just log the commands that would be sent
    println!("TSPL Commands to send to {}:\n{}", printer_address, tspl_commands);

    Ok("Label printed successfully".to_string())
}

fn generate_tspl_commands(sku: &str, name: &str, price: f64, barcode_data: &str) -> String {
    format!(
        r#"SIZE 40 mm, 31 mm
GAP 2 mm, 0 mm
DIRECTION 1
REFERENCE 0,0
OFFSET 0 mm
SET PEEL OFF
SET CUTTER OFF
SET PARTIAL_CUTTER OFF
SET TEAR ON
CLS

BARCODE 50,50,"128",60,1,0,2,2,"{}"
TEXT 50,120,"3",0,1,1,"{}"
TEXT 50,150,"2",0,1,1,"${:.2}"
TEXT 50,180,"1",0,1,1,"{}"

PRINT 1,1
"#,
        barcode_data, name, price, sku
    )
}

// Test print functionality
#[tauri::command]
pub async fn print_test_label(
    token: String,
    printer_address: String,
) -> Result<String, String> {
    check_permission(&token, "inventory_management").await?;

    print_barcode_label(
        token,
        "123456789012".to_string(),
        "Test Product".to_string(),
        9.99,
        Some(printer_address),
    ).await
}

// Receipt printing functionality
#[tauri::command]
pub async fn print_receipt_html(
    token: String,
    receipt_html: String,
) -> Result<String, String> {
    check_permission(&token, "sales_management").await?;

    // Use tauri-plugin-printer to print HTML content
    // This will open the system print dialog
    println!("Printing receipt with HTML content:\n{}", receipt_html);

    // TODO: Implement actual printing using tauri-plugin-printer
    // For now, return success
    Ok("Receipt printed successfully".to_string())
}
